import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon } from 'lucide-react';

interface DatePickerButtonProps {
  date: Date | undefined;
  onDateSelect: (date: Date | undefined) => void;
  placeholder?: string;
}

export const DatePickerButton: React.FC<DatePickerButtonProps> = ({
  date,
  onDateSelect,
  placeholder = "Select date"
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const handleDateSelect = (selectedDate: Date | undefined) => {
    onDateSelect(selectedDate);
    setIsOpen(false);
  };

  const formatDate = (date: Date | undefined) => {
    if (!date) return placeholder;
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="h-8 px-3 text-sm font-medium bg-[#1a1a1a] border-[#2a2a2a] hover:bg-[#2a2a2a]"
        >
          <CalendarIcon className="h-3 w-3 mr-1" />
          {formatDate(date)}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar
          mode="single"
          selected={date}
          onSelect={handleDateSelect}
          captionLayout="dropdown"
          className="rounded-lg border shadow-sm"
        />
      </PopoverContent>
    </Popover>
  );
};

export default DatePickerButton;
