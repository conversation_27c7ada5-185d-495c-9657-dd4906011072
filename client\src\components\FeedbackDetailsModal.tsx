import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, RefreshCw, MessageSquare } from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { DateRange } from 'react-day-picker';
import { subDays } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import {
  getHappyOrNotSettings,
  syncHappyOrNotFeedback,
  HappyOrNotSettings
} from '@/api/happyOrNot';
import HappyOrNotFeedbackList from '@/components/HappyOrNotFeedbackList';

interface FeedbackDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function FeedbackDetailsModal({ open, onOpenChange }: FeedbackDetailsModalProps) {
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [settings, setSettings] = useState<HappyOrNotSettings | null>(null);
  const [dateRange, setDateRange] = useState<DateRange>({
    from: undefined,
    to: undefined
  });

  const { toast } = useToast();

  // Load settings when modal opens
  useEffect(() => {
    if (open) {
      fetchSettings();
    }
  }, [open]);

  // Fetch settings
  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await getHappyOrNotSettings();

      if (response.success) {
        setSettings(response.data);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load Happy or Not settings',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching Happy or Not settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to load Happy or Not settings',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Sync feedback data
  const syncFeedbackData = async () => {
    setSyncing(true);
    try {
      const response = await syncHappyOrNotFeedback();

      if (response.success) {
        toast({
          title: 'Success',
          description: 'Feedback data synced successfully',
        });
      } else {
        toast({
          title: 'Error',
          description: 'Failed to sync feedback data',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error syncing feedback data:', error);
      toast({
        title: 'Error',
        description: 'Failed to sync feedback data',
        variant: 'destructive',
      });
    } finally {
      setSyncing(false);
    }
  };

  // Handle date range change
  const handleDateRangeChange = (range: DateRange | undefined) => {
    if (range) {
      setDateRange(range);
    }
  };

  // Format experience point from settings for the component
  const formattedExperiencePoints = settings?.experiencePointName ? [
    {
      id: "1", // Using a placeholder ID since we're only using the name
      name: settings.experiencePointName
    }
  ] : [];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden flex flex-col bg-[#1a1a1a] border-[#2a2a2a]">
        <DialogHeader className="flex-shrink-0">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <DialogTitle className="text-2xl font-bold tracking-tight flex items-center gap-2">
                <MessageSquare className="h-6 w-6" />
                Feedback Details
              </DialogTitle>
              <DialogDescription className="text-muted-foreground">
                Search and filter customer feedback comments and responses
              </DialogDescription>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                variant="outline"
                onClick={syncFeedbackData}
                disabled={syncing}
                size="sm"
              >
                {syncing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Syncing...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Sync Data
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          {loading ? (
            <div className="flex justify-center items-center h-80">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : (
            <div className="h-full overflow-auto">
              <HappyOrNotFeedbackList
                experiencePoints={formattedExperiencePoints}
                dateRange={dateRange}
                onDateRangeChange={handleDateRangeChange}
              />
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default FeedbackDetailsModal;
