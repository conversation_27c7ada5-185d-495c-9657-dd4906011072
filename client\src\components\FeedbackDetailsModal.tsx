import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MessageSquare, Search } from 'lucide-react';
import { DateRange } from '@/components/ui/date-range-presets';
import { DatePickerButton } from '@/components/ui/date-picker-button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';

import HappyOrNotFeedbackList from '@/components/HappyOrNotFeedbackList';

interface FeedbackDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function FeedbackDetailsModal({ open, onOpenChange }: FeedbackDetailsModalProps) {
  const [dateRange, setDateRange] = useState<DateRange>(() => {
    // Default to no date restrictions (show all feedback)
    return {
      startDate: undefined,
      endDate: undefined,
      label: 'All Time'
    };
  });
  const [currentPreset, setCurrentPreset] = useState<string>('All Time');

  // Filter states
  const [selectedButtonIndex, setSelectedButtonIndex] = useState<string>('all');
  const [searchText, setSearchText] = useState<string>('');
  const [showWithoutComments, setShowWithoutComments] = useState<boolean>(true);

  // Helper functions for date calculations
  const getFirstDayOfMonth = () => {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth(), 1);
  };

  const getFirstDayOfWeek = () => {
    const now = new Date();
    const day = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const diff = now.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    return new Date(now.setDate(diff));
  };

  const getFirstDayOfYear = () => {
    const now = new Date();
    return new Date(now.getFullYear(), 0, 1);
  };

  const getEndOfToday = () => {
    const now = new Date();
    now.setHours(23, 59, 59, 999);
    return now;
  };

  // Date range presets
  const presets = {
    wtd: {
      label: 'Week to Date',
      startDate: getFirstDayOfWeek(),
      endDate: getEndOfToday(),
    },
    mtd: {
      label: 'Month to Date',
      startDate: getFirstDayOfMonth(),
      endDate: getEndOfToday(),
    },
    ytd: {
      label: 'Year to Date',
      startDate: getFirstDayOfYear(),
      endDate: getEndOfToday(),
    },
    last7Days: {
      label: 'Last 7 Days',
      startDate: new Date(new Date().setDate(new Date().getDate() - 7)),
      endDate: getEndOfToday(),
    },
    last14Days: {
      label: 'Last 14 Days',
      startDate: new Date(new Date().setDate(new Date().getDate() - 14)),
      endDate: getEndOfToday(),
    },
    lastMonth: {
      label: 'Last Month',
      startDate: new Date(new Date().setDate(new Date().getDate() - 30)),
      endDate: getEndOfToday(),
    },
    last4Months: {
      label: 'Last 4 Months',
      startDate: new Date(new Date().setMonth(new Date().getMonth() - 4)),
      endDate: getEndOfToday(),
    },
    last6Months: {
      label: 'Last 6 Months',
      startDate: new Date(new Date().setMonth(new Date().getMonth() - 6)),
      endDate: getEndOfToday(),
    },
    lastYear: {
      label: 'Last Year',
      startDate: new Date(new Date().setFullYear(new Date().getFullYear() - 1)),
      endDate: getEndOfToday(),
    },
    allTime: {
      label: 'All Time',
      startDate: undefined,
      endDate: undefined,
    },
  };

  // Handle preset selection
  const handlePresetSelect = (preset: keyof typeof presets | DateRange) => {
    if (typeof preset === 'string') {
      const selectedPreset = presets[preset];
      setDateRange({
        startDate: selectedPreset.startDate,
        endDate: selectedPreset.endDate,
        label: selectedPreset.label
      });
      setCurrentPreset(selectedPreset.label);
    } else {
      // Handle custom date range
      setDateRange(preset);
      setCurrentPreset(preset.label || 'Custom Range');
    }
  };

  // Get preset key from current preset label
  const getPresetKey = (presetLabel: string): string => {
    const presetEntry = Object.entries(presets).find(([_, preset]) => preset.label === presetLabel);
    return presetEntry ? presetEntry[0] : 'custom';
  };

  // Handle individual date selection
  const handleDateSelect = (date: Date | undefined, type: 'start' | 'end') => {
    if (!date) return;

    let newStartDate = dateRange.startDate;
    let newEndDate = dateRange.endDate;

    if (type === 'start') {
      newStartDate = date;
      // If start date is after end date, adjust end date
      if (newEndDate && date > newEndDate) {
        newEndDate = new Date(date);
        newEndDate.setHours(23, 59, 59, 999);
      }
    } else {
      // Set end date to end of day
      newEndDate = new Date(date);
      newEndDate.setHours(23, 59, 59, 999);
      // If end date is before start date, adjust start date
      if (newStartDate && date < newStartDate) {
        newStartDate = date;
      }
    }

    setDateRange({
      startDate: newStartDate,
      endDate: newEndDate,
      label: 'Custom Range'
    });
    setCurrentPreset('Custom Range');
  };



  // Handle date range change - convert between DateRange types
  const handleDateRangeChange = (range: any) => {
    if (range) {
      // Convert from react-day-picker DateRange to our DateRange
      setDateRange({
        startDate: range.from,
        endDate: range.to,
        label: 'Custom Range'
      });
      setCurrentPreset('Custom Range');
    }
  };

  // No experience points needed for modal context
  const formattedExperiencePoints: Array<{ id: string; name: string }> = [];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl h-[90vh] overflow-hidden flex flex-col bg-[#1a1a1a] border-[#2a2a2a]">
        <DialogHeader className="flex-shrink-0">
          <div>
            <DialogTitle className="text-2xl font-bold tracking-tight flex items-center gap-2">
              <MessageSquare className="h-6 w-6" />
              Feedback Details
            </DialogTitle>
            <DialogDescription className="text-muted-foreground">
              Search and filter customer feedback comments and responses
            </DialogDescription>
          </div>
        </DialogHeader>

        {/* Date Range and Filter Controls */}
        <div className="flex-shrink-0 px-6">
          <Card className="bg-[#1a1a1a] border-[#2a2a2a]">
            <CardContent className="p-4">
              {/* Filter Controls */}
              <div className="flex items-center gap-4">
                {/* Date Range Display */}
                <div className="flex items-center gap-1">
                  <DatePickerButton
                    date={dateRange.startDate}
                    onDateSelect={(date) => handleDateSelect(date, 'start')}
                    placeholder="Start Date"
                  />
                  <span className="text-muted-foreground">-</span>
                  <DatePickerButton
                    date={dateRange.endDate}
                    onDateSelect={(date) => handleDateSelect(date, 'end')}
                    placeholder="End Date"
                  />
                </div>

                {/* Preset Selector */}
                <Select
                  value={getPresetKey(currentPreset)}
                  onValueChange={(value) => {
                    if (value === 'custom') {
                      return;
                    }
                    handlePresetSelect(value as keyof typeof presets);
                  }}
                >
                  <SelectTrigger className="w-20 h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="allTime">All</SelectItem>
                    <SelectItem value="wtd">WTD</SelectItem>
                    <SelectItem value="mtd">MTD</SelectItem>
                    <SelectItem value="ytd">YTD</SelectItem>
                    <SelectItem value="last7Days">7D</SelectItem>
                    <SelectItem value="last14Days">14D</SelectItem>
                    <SelectItem value="lastMonth">1M</SelectItem>
                    <SelectItem value="last4Months">4M</SelectItem>
                    <SelectItem value="last6Months">6M</SelectItem>
                    <SelectItem value="lastYear">1Y</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>

                {/* Search */}
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search in comments..."
                    className="pl-8 h-8"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                  />
                </div>

                {/* Rating Filter */}
                <Select
                  value={selectedButtonIndex}
                  onValueChange={setSelectedButtonIndex}
                >
                  <SelectTrigger className="w-32 h-8">
                    <SelectValue placeholder="All Ratings" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Ratings</SelectItem>
                    <SelectItem value="0">
                      <div className="flex items-center gap-2">
                        <img src="/img/happyornot/veryangry.svg" alt="Very Unhappy" className="w-5 h-5" />
                        <span>Very Unhappy</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="1">
                      <div className="flex items-center gap-2">
                        <img src="/img/happyornot/angry.svg" alt="Unhappy" className="w-5 h-5" />
                        <span>Unhappy</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="2">
                      <div className="flex items-center gap-2">
                        <img src="/img/happyornot/happy.svg" alt="Happy" className="w-5 h-5" />
                        <span>Happy</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="3">
                      <div className="flex items-center gap-2">
                        <img src="/img/happyornot/veryhappy.svg" alt="Very Happy" className="w-5 h-5" />
                        <span>Very Happy</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>

                {/* Show Without Comments Toggle */}
                <div className="flex items-center space-x-2">
                  <Switch
                    id="show-without-comments"
                    checked={showWithoutComments}
                    onCheckedChange={setShowWithoutComments}
                  />
                  <Label htmlFor="show-without-comments" className="text-sm text-muted-foreground whitespace-nowrap">
                    Show without comments
                  </Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="flex-1 overflow-hidden px-6">
          <div className="h-full overflow-auto">
            <HappyOrNotFeedbackList
              experiencePoints={formattedExperiencePoints}
              dateRange={{
                from: dateRange.startDate,
                to: dateRange.endDate
              }}
              onDateRangeChange={handleDateRangeChange}
              isModal={true}
              selectedButtonIndex={selectedButtonIndex}
              searchText={searchText}
              showWithoutComments={showWithoutComments}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default FeedbackDetailsModal;
