import React, { useState, useEffect } from 'react';
import { useToast } from '@/hooks/useToast';
import { getPaginatedHappyOrNotFeedback, PaginatedFeedbackOptions } from '@/api/happyOrNot';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Loader2, Search, Filter } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { DateRange } from 'react-day-picker';
import { format, subDays } from 'date-fns';
import { EnhancedDateRange } from '@/components/ui/enhanced-date-range';

interface Feedback {
  _id: string;
  feedbackId: string;
  buttonIndex: number;
  localTime: string;
  experiencePointId: string;
  experiencePointName: string;
  text?: string;
  textInEnglish?: string;
  followupOptionText?: string;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

interface FeedbackListProps {
  experiencePoints: Array<{ id: string; name: string }>;
  dateRange?: DateRange;
  onDateRangeChange?: (range: DateRange | undefined) => void;
  isModal?: boolean;
  selectedButtonIndex?: string;
  searchText?: string;
  showWithoutComments?: boolean;
}

const HappyOrNotFeedbackList: React.FC<FeedbackListProps> = ({
  experiencePoints,
  dateRange,
  onDateRangeChange,
  isModal = false,
  selectedButtonIndex: propSelectedButtonIndex,
  searchText: propSearchText,
  showWithoutComments: propShowWithoutComments,
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 20,
    pages: 0,
  });

  // Filters - use props when in modal mode, otherwise use local state
  const [selectedExperiencePoint, setSelectedExperiencePoint] = useState<string>('all');
  const [selectedButtonIndex, setSelectedButtonIndex] = useState<string>(propSelectedButtonIndex || 'all');
  const [searchText, setSearchText] = useState<string>(propSearchText || '');
  const [sortField, setSortField] = useState<string>('localTime');
  const [sortOrder, setSortOrder] = useState<string>('desc');
  const [localDateRange, setLocalDateRange] = useState<DateRange | undefined>(dateRange);

  // Use props when in modal mode
  const effectiveSelectedButtonIndex = isModal ? (propSelectedButtonIndex || 'all') : selectedButtonIndex;
  const effectiveSearchText = isModal ? (propSearchText || '') : searchText;
  const effectiveShowWithoutComments = isModal ? (propShowWithoutComments !== undefined ? propShowWithoutComments : true) : true;

  // Load feedback data
  const loadFeedbackData = async (page = 1) => {
    try {
      setLoading(true);

      // Build query parameters
      const options: PaginatedFeedbackOptions = {
        page,
        limit: pagination.limit,
        sortField,
        sortOrder: sortOrder as 'asc' | 'desc'
      };

      // Only filter by hasText if we don't want to show feedback without comments
      if (!effectiveShowWithoutComments) {
        options.hasText = true;
      }

      // Add date range if provided
      if (localDateRange?.from) {
        options.startDate = format(localDateRange.from, 'yyyy-MM-dd');
      }
      if (localDateRange?.to) {
        options.endDate = format(localDateRange.to, 'yyyy-MM-dd');
      }

      // Add filters if provided
      if (selectedExperiencePoint && selectedExperiencePoint !== 'all') {
        options.experiencePointId = selectedExperiencePoint;
      }
      if (effectiveSelectedButtonIndex && effectiveSelectedButtonIndex !== 'all') {
        options.buttonIndex = parseInt(effectiveSelectedButtonIndex);
      }
      if (effectiveSearchText) {
        options.searchText = effectiveSearchText;
      }

      // Make API request
      const response = await getPaginatedHappyOrNotFeedback(options);

      if (response.success) {
        setFeedbacks(response.data.feedbacks);
        setPagination(response.data.pagination);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to load feedback data',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error loading feedback data:', error);
      toast({
        title: 'Error',
        description: 'An error occurred while loading feedback data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle date range change
  const handleDateRangeChange = (range: DateRange | undefined) => {
    setLocalDateRange(range);
    if (onDateRangeChange) {
      onDateRangeChange(range);
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    loadFeedbackData(page);
  };

  // Handle filter change
  const handleFilterChange = () => {
    loadFeedbackData(1); // Reset to first page when filters change
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    loadFeedbackData(1); // Reset to first page when searching
  };

  // Get button color based on index
  const getButtonColor = (buttonIndex: number): string => {
    switch (buttonIndex) {
      case 0: return '#e24e67'; // Very Unhappy
      case 1: return '#ef9ea1'; // Unhappy
      case 2: return '#99cd9b'; // Happy
      case 3: return '#0daa5d'; // Very Happy
      default: return '#D9D9D9'; // Default - Gray
    }
  };

  // Get button label based on index
  const getButtonLabel = (buttonIndex: number): string => {
    switch (buttonIndex) {
      case 0: return 'Very Unhappy';
      case 1: return 'Unhappy';
      case 2: return 'Happy';
      case 3: return 'Very Happy';
      default: return `Rating ${buttonIndex}`;
    }
  };

  // Get face image based on index
  const getFaceImage = (buttonIndex: number): string => {
    switch (buttonIndex) {
      case 0: return '/img/happyornot/veryangry.svg';
      case 1: return '/img/happyornot/angry.svg';
      case 2: return '/img/happyornot/happy.svg';
      case 3: return '/img/happyornot/veryhappy.svg';
      default: return '/img/happyornot/happy.svg';
    }
  };

  // Generate pagination items
  const renderPaginationItems = () => {
    const items = [];
    const maxVisiblePages = 5;

    // Always show first page
    items.push(
      <PaginationItem key="first">
        <PaginationLink
          onClick={() => handlePageChange(1)}
          isActive={pagination.page === 1}
        >
          1
        </PaginationLink>
      </PaginationItem>
    );

    // Show ellipsis if needed
    if (pagination.page > 3) {
      items.push(
        <PaginationItem key="ellipsis-start">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }

    // Show pages around current page
    const startPage = Math.max(2, pagination.page - 1);
    const endPage = Math.min(pagination.pages - 1, pagination.page + 1);

    for (let i = startPage; i <= endPage; i++) {
      if (i > 1 && i < pagination.pages) {
        items.push(
          <PaginationItem key={i}>
            <PaginationLink
              onClick={() => handlePageChange(i)}
              isActive={pagination.page === i}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }
    }

    // Show ellipsis if needed
    if (pagination.page < pagination.pages - 2) {
      items.push(
        <PaginationItem key="ellipsis-end">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }

    // Always show last page if there's more than one page
    if (pagination.pages > 1) {
      items.push(
        <PaginationItem key="last">
          <PaginationLink
            onClick={() => handlePageChange(pagination.pages)}
            isActive={pagination.page === pagination.pages}
          >
            {pagination.pages}
          </PaginationLink>
        </PaginationItem>
      );
    }

    return items;
  };

  // Load initial data
  useEffect(() => {
    loadFeedbackData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Reload data when filter props change in modal mode
  useEffect(() => {
    if (isModal) {
      loadFeedbackData(1);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [propSelectedButtonIndex, propSearchText, propShowWithoutComments, dateRange]);

  return (
    <Card className={`w-full ${isModal ? 'bg-[#1a1a1a] border-[#2a2a2a]' : ''}`}>
      {!isModal && (
        <CardHeader>
          <CardTitle>Feedback Comments</CardTitle>
          <CardDescription>
            View and search customer feedback comments and follow-up responses
          </CardDescription>
        </CardHeader>
      )}

      <CardContent>
        {/* Filters - only show when not in modal mode */}
        {!isModal && (
          <div className="mb-6">
            <Card className="border shadow-sm">
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">Feedback Filters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col lg:flex-row gap-4">
                {/* Date Range */}
                <div className="w-full lg:w-1/3">
                  <Label htmlFor="date-range" className="mb-2 block">Date Range</Label>
                  <EnhancedDateRange
                    onSelectDateRange={(range) => {
                      handleDateRangeChange({
                        from: range.startDate,
                        to: range.endDate
                      });
                    }}
                    currentDateRange={{
                      startDate: localDateRange?.from,
                      endDate: localDateRange?.to
                    }}
                    currentPreset={localDateRange?.from && localDateRange?.to ?
                      `${format(localDateRange.from, 'MMM d, yyyy')} - ${format(localDateRange.to, 'MMM d, yyyy')}` :
                      'Select Date Range'}
                  />
                </div>

                {/* Filters */}
                <div className="w-full lg:w-1/3 space-y-4">
                  {!isModal && (
                    <div>
                      <Label htmlFor="experience-point" className="mb-2 block">Experience Point</Label>
                      <Select
                        value={selectedExperiencePoint}
                        onValueChange={setSelectedExperiencePoint}
                      >
                        <SelectTrigger id="experience-point">
                          <SelectValue placeholder="All Experience Points" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Experience Points</SelectItem>
                          {experiencePoints.map((point) => (
                            <SelectItem key={point.id} value={point.id}>
                              {point.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  <div>
                    <Label htmlFor="button-index" className="mb-2 block">Rating</Label>
                    <Select
                      value={selectedButtonIndex}
                      onValueChange={setSelectedButtonIndex}
                    >
                      <SelectTrigger id="button-index">
                        <SelectValue placeholder="All Ratings" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Ratings</SelectItem>
                        <SelectItem value="0">
                          <div className="flex items-center gap-2">
                            <img src="/img/happyornot/veryangry.svg" alt="Very Unhappy" className="w-5 h-5" />
                            <span>Very Unhappy</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="1">
                          <div className="flex items-center gap-2">
                            <img src="/img/happyornot/angry.svg" alt="Unhappy" className="w-5 h-5" />
                            <span>Unhappy</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="2">
                          <div className="flex items-center gap-2">
                            <img src="/img/happyornot/happy.svg" alt="Happy" className="w-5 h-5" />
                            <span>Happy</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="3">
                          <div className="flex items-center gap-2">
                            <img src="/img/happyornot/veryhappy.svg" alt="Very Happy" className="w-5 h-5" />
                            <span>Very Happy</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Search */}
                <div className="w-full lg:w-1/3 space-y-4">
                  <div>
                    <Label htmlFor="search-text" className="mb-2 block">Search</Label>
                    <form onSubmit={handleSearch} className="flex gap-2">
                      <div className="flex-1">
                        <div className="relative">
                          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input
                            id="search-text"
                            placeholder="Search in comments..."
                            className="pl-8"
                            value={searchText}
                            onChange={(e) => setSearchText(e.target.value)}
                          />
                        </div>
                      </div>
                      <Button type="submit">Search</Button>
                    </form>
                  </div>

                  <div className="flex justify-between gap-2 pt-2">
                    <Button variant="outline" onClick={() => {
                      setSelectedExperiencePoint('all');
                      setSelectedButtonIndex('all');
                      setSearchText('');
                      // Reset to default date range
                      const defaultDateRange = {
                        from: subDays(new Date(), 30),
                        to: new Date()
                      };
                      handleDateRangeChange(defaultDateRange);
                      // Load data with reset filters
                      setTimeout(() => loadFeedbackData(1), 0);
                    }}>
                      Reset Filters
                    </Button>

                    <Button variant="outline" onClick={handleFilterChange}>
                      <Filter className="mr-2 h-4 w-4" />
                      Apply Filters
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        )}

        {/* Results */}
        {loading ? (
          <div className="flex justify-center items-center h-40">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : feedbacks.length > 0 ? (
          <div className={isModal ? "space-y-1" : "space-y-4"}>
            {feedbacks.map((feedback) => (
              isModal ? (
                // Modal styling - matches main page recent feedback stream (no container)
                <div
                  key={feedback.feedbackId}
                  className="flex items-start gap-3 p-3 hover:bg-[#1a1a1a] transition-colors"
                >
                  <img
                    src={getFaceImage(feedback.buttonIndex)}
                    alt={getButtonLabel(feedback.buttonIndex)}
                    className="w-8 h-8 flex-shrink-0"
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm">
                        {getButtonLabel(feedback.buttonIndex)}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {new Date(feedback.localTime).toLocaleString()}
                      </span>
                    </div>
                    {feedback.text && (
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {feedback.text}
                      </p>
                    )}
                    {feedback.textInEnglish && feedback.textInEnglish !== feedback.text && (
                      <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                        <span className="font-medium">English:</span> {feedback.textInEnglish}
                      </p>
                    )}
                    {feedback.followupOptionText && (
                      <p className="text-sm text-blue-400 line-clamp-1 mt-1">
                        Follow-up: {feedback.followupOptionText}
                      </p>
                    )}
                    {feedback.experiencePointName && (
                      <p className="text-xs text-muted-foreground mt-1">
                        {feedback.experiencePointName}
                      </p>
                    )}
                  </div>
                </div>
              ) : (
                // Original styling for non-modal use
                <div
                  key={feedback.feedbackId}
                  className="p-4 border rounded-lg"
                  style={{ borderLeftColor: getButtonColor(feedback.buttonIndex), borderLeftWidth: '4px' }}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-3">
                      <img
                        src={getFaceImage(feedback.buttonIndex)}
                        alt={getButtonLabel(feedback.buttonIndex)}
                        className="w-10 h-10"
                      />
                      <div>
                        <p className="font-medium">{getButtonLabel(feedback.buttonIndex)}</p>
                        <p className="text-sm text-muted-foreground">
                          {new Date(feedback.localTime).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm">{feedback.experiencePointName}</p>
                    </div>
                  </div>

                  <div className="mt-2 pt-2 border-t">
                    <div className="mb-2">
                      <p className="text-xs font-medium text-muted-foreground">Customer Comment:</p>
                      <p className="text-sm italic">"{feedback.text}"</p>
                      {feedback.textInEnglish && feedback.textInEnglish !== feedback.text && (
                        <p className="text-sm text-muted-foreground mt-1">
                          <span className="font-medium">English:</span> "{feedback.textInEnglish}"
                        </p>
                      )}
                    </div>

                    {feedback.followupOptionText && (
                      <div className="mt-3 pt-2 border-t border-dashed">
                        <p className="text-xs font-medium text-muted-foreground">Follow-up Response:</p>
                        <p className="text-sm">
                          {feedback.followupOptionText}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )
            ))}
          </div>
        ) : (
          <div className="flex justify-center items-center h-40 text-muted-foreground">
            No feedback found matching the selected filters
          </div>
        )}
      </CardContent>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <CardFooter>
          <Pagination className="w-full justify-center">
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => handlePageChange(Math.max(1, pagination.page - 1))}
                  disabled={pagination.page === 1}
                />
              </PaginationItem>

              {renderPaginationItems()}

              <PaginationItem>
                <PaginationNext
                  onClick={() => handlePageChange(Math.min(pagination.pages, pagination.page + 1))}
                  disabled={pagination.page === pagination.pages}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </CardFooter>
      )}
    </Card>
  );
};

export default HappyOrNotFeedbackList;
